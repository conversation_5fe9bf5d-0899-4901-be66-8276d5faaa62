import { Inter, Poppins } from 'next/font/google'
import './globals.css'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

export const metadata = {
  title: {
    default: 'The White Laces | Luxury Streetwear Footwear',
    template: '%s | The White Laces'
  },
  description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands. Mexico-first, LATAM-ready.',
  keywords: ['luxury sneakers', 'streetwear', 'limited edition', 'premium footwear', 'Mexico', 'LATAM'],
  authors: [{ name: 'The White Laces Team' }],
  creator: 'The White Laces',
  publisher: 'The White Laces',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://thewhitelaces.com'),
  alternates: {
    canonical: '/',
    languages: {
      'es-MX': '/es-MX',
      'en-US': '/en-US',
      'pt-BR': '/pt-BR',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_MX',
    url: 'https://thewhitelaces.com',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    siteName: 'The White Laces',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'The White Laces - Luxury Streetwear Footwear',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    images: ['/og-image.jpg'],
    creator: '@thewhitelaces',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="es-MX" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${poppins.variable} font-poppins antialiased`}
        suppressHydrationWarning
      >
        <div className="min-h-screen bg-white text-gray-900">
          {/* Simple Header */}
          <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <h1 className="text-2xl font-bold text-lime-600">The White Laces</h1>
                </div>
                <nav className="hidden md:flex space-x-8">
                  <a href="/" className="text-gray-900 hover:text-lime-600 transition-colors">Inicio</a>
                  <a href="/shop" className="text-gray-900 hover:text-lime-600 transition-colors">Tienda</a>
                  <a href="/brands" className="text-gray-900 hover:text-lime-600 transition-colors">Marcas</a>
                  <a href="/cart" className="text-gray-900 hover:text-lime-600 transition-colors">Carrito</a>
                </nav>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main>
            {children}
          </main>

          {/* Simple Footer */}
          <footer className="bg-gray-900 text-white py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-lime-500 mb-4">The White Laces</h3>
                <p className="text-gray-400 mb-4">Luxury streetwear footwear - México primero, LATAM listo</p>
                <div className="flex justify-center space-x-6">
                  <a href="#" className="text-gray-400 hover:text-lime-500 transition-colors">Instagram</a>
                  <a href="#" className="text-gray-400 hover:text-lime-500 transition-colors">TikTok</a>
                  <a href="#" className="text-gray-400 hover:text-lime-500 transition-colors">Twitter</a>
                </div>
                <div className="mt-8 pt-8 border-t border-gray-800 text-sm text-gray-500">
                  © 2024 The White Laces. Todos los derechos reservados.
                </div>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}
