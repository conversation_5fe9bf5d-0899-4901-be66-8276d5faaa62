'use client'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative min-h-screen bg-gradient-to-br from-white via-gray-50 to-gray-100 flex items-center justify-center overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #BFFF00 0%, transparent 50%),
                             radial-gradient(circle at 75% 75%, #000000 0%, transparent 50%)`
          }} />
        </div>

        {/* Main Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            {/* Badge */}
            <div className="inline-flex">
              <div className="bg-white/80 backdrop-blur-sm px-6 py-3 text-sm font-medium text-lime-600 border border-lime-200 rounded-full">
                ✨ Descubre calzado streetwear de lujo en The White Laces
              </div>
            </div>

            {/* Main Title */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-black px-4">
              Lujo Urbano Redefinido
            </h1>

            {/* Description */}
            <p className="text-base sm:text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed px-4">
              Sneakers premium, ediciones limitadas y drops exclusivos de las mejores marcas. México primero, LATAM listo.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center pt-8 px-4">
              <button className="w-full sm:w-auto min-w-[200px] bg-lime-500 hover:bg-lime-600 text-black font-medium py-3 px-6 rounded-lg transition-colors">
                Explorar Colección
              </button>
              <button className="w-full sm:w-auto min-w-[200px] bg-gray-800 hover:bg-gray-900 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                Ver Drops Limitados
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 pt-12 sm:pt-16 max-w-2xl mx-auto px-4">
              {[
                { value: "500+", label: "Marcas Premium" },
                { value: "50K+", label: "Clientes Satisfechos" },
                { value: "24/7", label: "Soporte Premium" }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-lime-600">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Drops Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-lime-600 mb-4">
              Drops Destacados
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Las últimas colaboraciones y lanzamientos exclusivos que están causando sensación en la escena streetwear.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Placeholder cards */}
            {[1, 2, 3].map((item) => (
              <div key={item} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="h-64 bg-gray-200"></div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Producto {item}</h3>
                  <p className="text-gray-600 mb-4">Descripción del producto premium</p>
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-lime-600">$3,570 MXN</span>
                    <button className="bg-lime-500 hover:bg-lime-600 text-black px-4 py-2 rounded-lg transition-colors">
                      Ver Más
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Mantente al Día
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-lime-500"
            />
            <button className="bg-lime-500 hover:bg-lime-600 text-black font-medium px-6 py-3 rounded-lg transition-colors whitespace-nowrap">
              Suscribirse
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}
